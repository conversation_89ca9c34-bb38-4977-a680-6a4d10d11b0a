#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Teste direto de conversão FBX para validação
"""

import os
import sys
import logging
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Teste direto de conversão"""
    logger.info("🚀 TESTE DIRETO DE CONVERSÃO FBX")
    logger.info("=" * 50)
    
    # Arquivo de entrada
    input_file = r"C:\Users\<USER>\Downloads\Furniture_Chairs_Plank_Blocco-Chair.rfa"
    output_file = "chair_converted_direct.fbx"
    
    # Verificar se arquivo existe
    logger.info(f"📁 Verificando arquivo de entrada: {input_file}")
    if os.path.exists(input_file):
        file_size = os.path.getsize(input_file)
        logger.info(f"✅ Arquivo encontrado! Tamanho: {file_size} bytes")
    else:
        logger.error(f"❌ Arquivo não encontrado: {input_file}")
        return 1
    
    # Verificar diretório atual
    current_dir = os.getcwd()
    logger.info(f"📂 Diretório atual: {current_dir}")
    
    # Listar arquivos no diretório
    logger.info("📋 Arquivos no diretório:")
    try:
        files = os.listdir(current_dir)
        for f in files[:10]:  # Mostrar apenas os primeiros 10
            logger.info(f"  - {f}")
        if len(files) > 10:
            logger.info(f"  ... e mais {len(files) - 10} arquivos")
    except Exception as e:
        logger.error(f"❌ Erro ao listar arquivos: {e}")
    
    # Tentar importar o conversor
    logger.info("🔧 Tentando importar conversor...")
    try:
        from bimex_converter import convert_rfa_to_fbx
        logger.info("✅ Conversor importado com sucesso!")
        
        # Executar conversão
        logger.info("🔄 Iniciando conversão...")
        result = convert_rfa_to_fbx(input_file, output_file)
        
        # Mostrar resultado
        logger.info("📊 RESULTADO DA CONVERSÃO:")
        logger.info(f"  Sucesso: {result.get('success', False)}")
        logger.info(f"  Entrada: {result.get('input_path', 'N/A')}")
        logger.info(f"  Saída: {result.get('output_path', 'N/A')}")
        logger.info(f"  Formato: {result.get('format', 'N/A')}")
        logger.info(f"  Qualidade: {result.get('quality', 'N/A')}")
        
        if result.get('error'):
            logger.error(f"  Erro: {result.get('error')}")
        
        if result.get('features'):
            logger.info("  Recursos preservados:")
            for feature in result.get('features', []):
                logger.info(f"    - {feature}")
        
        # Verificar arquivo de saída
        if os.path.exists(output_file):
            output_size = os.path.getsize(output_file)
            logger.info(f"✅ Arquivo FBX criado: {output_file}")
            logger.info(f"📏 Tamanho: {output_size} bytes")
            
            # Analisar conteúdo
            try:
                with open(output_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()[:1000]  # Primeiros 1000 caracteres
                
                logger.info("🔍 Análise do arquivo FBX:")
                if "FBX" in content:
                    logger.info("  ✅ Cabeçalho FBX encontrado")
                if "BIMEX" in content:
                    logger.info("  ✅ Assinatura BIMEX encontrada")
                if "Geometry" in content:
                    logger.info("  ✅ Geometria presente")
                if "Material" in content:
                    logger.info("  ✅ Materiais incluídos")
                    
            except Exception as e:
                logger.warning(f"⚠️ Erro ao analisar arquivo: {e}")
        else:
            logger.error(f"❌ Arquivo FBX não foi criado: {output_file}")
        
        return 0 if result.get('success') else 1
        
    except ImportError as e:
        logger.error(f"❌ Erro ao importar conversor: {e}")
        return 1
    except Exception as e:
        logger.error(f"❌ Erro durante conversão: {e}")
        return 1

if __name__ == "__main__":
    try:
        result = main()
        logger.info("=" * 50)
        if result == 0:
            logger.info("🎉 TESTE CONCLUÍDO COM SUCESSO!")
        else:
            logger.info("❌ TESTE FALHOU")
        logger.info("=" * 50)
        sys.exit(result)
    except Exception as e:
        logger.error(f"❌ Erro fatal: {e}")
        sys.exit(1)
